"use client";

import Link from "next/link";
import Image from "next/image";
import { useTranslations, useLocale } from 'next-intl';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Coffee, Package, Gift, ArrowRight, Truck, DollarSign, Star } from "lucide-react";

export default function Home() {
  const t = useTranslations();
  const locale = useLocale();

  // Generate localized paths with proper locale handling
  const getLocalizedPath = (path: string) => {
    // Always include locale prefix (consistent with middleware localePrefix: 'always')
    return `/${locale}${path}`;
  };

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-gradient-hero py-20 lg:py-32 overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-primary/20 to-transparent rounded-full animate-float"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-secondary/20 to-transparent rounded-full animate-float" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-20 left-1/4 w-20 h-20 bg-gradient-to-br from-accent/20 to-transparent rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Text content */}
            <div className="max-w-2xl mx-auto lg:mx-0 text-center lg:text-left animate-fade-in-up">
              <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight mb-6">
                <span className="bg-gradient-to-r from-foreground via-primary/80 to-foreground bg-[length:200%_100%]
                  bg-clip-text text-transparent animate-gradient-x">
                  {t('homepage.hero.title')}
                </span>
              </h1>

              <p className="text-xl text-muted-foreground mb-8 animate-fade-in-up leading-relaxed"
                style={{animationDelay: '0.2s'}}>
                <span className="bg-gradient-to-r from-muted-foreground via-foreground/70 to-muted-foreground
                  bg-[length:200%_100%] bg-clip-text text-transparent animate-gradient-x-slow">
                  {t('homepage.hero.description')}
                </span>
              </p>

              {/* Features List */}
              <div className="mb-8 animate-fade-in-up" style={{animationDelay: '0.3s'}}>
                <ul className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-lg mx-auto lg:mx-0">
                  {(t.raw('homepage.hero.features') as string[]).map((feature, index) => {
                    // Icone specifiche per ogni feature
                    const icons = [Truck, DollarSign, Gift, Star];
                    const IconComponent = icons[index] || Star;

                    return (
                      <li key={index} className="flex items-center gap-3 text-muted-foreground py-2
                        bg-muted/20 rounded-lg px-3 hover:bg-muted/30 transition-colors">
                        <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg
                          flex items-center justify-center flex-shrink-0">
                          <IconComponent className="w-4 h-4 text-primary" />
                        </div>
                        <span className="text-sm font-medium leading-relaxed">{feature}</span>
                      </li>
                    );
                  })}
                </ul>
              </div>

              {/* Primary CTA - Coffee Box Builder */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8 animate-fade-in-up"
                style={{animationDelay: '0.4s'}}>
                <Button size="lg" className="text-lg px-8 py-6 h-auto shadow-glow hover:shadow-glow-hover group" asChild>
                  <Link href={getLocalizedPath('/coffee-box-builder')}>
                    <Package className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform-smooth" />
                    {t('homepage.hero.ctaPrimary')}
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform-smooth" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" className="text-lg px-8 py-6 h-auto group" asChild>
                  <Link href={getLocalizedPath('/shop')}>
                    <span className="group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-primary/80 group-hover:bg-clip-text group-hover:text-transparent transition-all-smooth">
                      {t('homepage.hero.ctaSecondary')}
                    </span>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Hero Image */}
            <div className="hidden lg:flex justify-center items-center animate-fade-in-up order-first lg:order-last"
              style={{animationDelay: '0.5s'}}>
              <div className="relative w-full max-w-lg rounded-2xl overflow-hidden border border-muted/20
                bg-gradient-to-br from-muted/10 to-muted/5
                shadow-[0_20px_50px_rgba(0,0,0,0.15),0_10px_25px_rgba(0,0,0,0.1),0_4px_10px_rgba(0,0,0,0.08)]
                hover:shadow-[0_25px_60px_rgba(0,0,0,0.2),0_15px_35px_rgba(0,0,0,0.15),0_8px_15px_rgba(0,0,0,0.1)]
                transition-all duration-500 ease-out hover:scale-[1.02]">
                <Image
                  src="/images/hero-coffee-image.jpg"
                  alt="Caffè Italiano Premium - PrimeCaffe"
                  width={500}
                  height={400}
                  className="w-full h-auto object-cover"
                  priority
                  sizes="(max-width: 768px) 0px, (max-width: 1024px) 400px, 500px"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent"></div>
                {/* Premium glow effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 rounded-2xl blur-sm opacity-0 hover:opacity-100 transition-opacity duration-500 -z-10"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-muted/30 via-background to-muted/20 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 right-10 w-40 h-40 bg-gradient-to-br from-primary/10 to-transparent rounded-full animate-float" style={{animationDelay: '0.5s'}}></div>
          <div className="absolute bottom-10 left-10 w-32 h-32 bg-gradient-to-br from-secondary/10 to-transparent rounded-full animate-float" style={{animationDelay: '1.5s'}}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {t('homepage.features.title')}
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              {t('homepage.features.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center group hover-glow animate-fade-in-up" style={{animationDelay: '0.1s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-soft group-hover:shadow-glow">
                  <Package className="h-6 w-6 text-primary group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.coffeeBoxBuilder.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.coffeeBoxBuilder.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={getLocalizedPath('/coffee-box-builder')}>{t('homepage.features.coffeeBoxBuilder.cta')}</Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center group hover-glow animate-fade-in-up" style={{animationDelay: '0.2s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-soft group-hover:shadow-glow">
                  <Gift className="h-6 w-6 text-primary group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.bundles.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.bundles.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={getLocalizedPath('/bundles')}>{t('homepage.features.bundles.cta')}</Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center group hover-glow animate-fade-in-up" style={{animationDelay: '0.3s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-soft group-hover:shadow-glow">
                  <Coffee className="h-6 w-6 text-primary group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.shop.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.shop.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={getLocalizedPath('/shop')}>{t('homepage.features.shop.cta')}</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-primary via-primary/95 to-primary/90 text-primary-foreground relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-1/4 w-24 h-24 bg-white/10 rounded-full animate-float"></div>
          <div className="absolute bottom-10 right-1/4 w-32 h-32 bg-white/5 rounded-full animate-float" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-10 w-16 h-16 bg-white/15 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container mx-auto px-4 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <div className="text-center lg:text-left animate-fade-in-up">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4">
                {t('homepage.finalCta.title')}
              </h2>
              <p className="text-xl mb-8 opacity-90 animate-fade-in-up" style={{animationDelay: '0.1s'}}>
                {t('homepage.finalCta.description')}
              </p>
              <Button size="lg" variant="secondary"
                className="text-lg px-8 py-6 h-auto shadow-strong hover:shadow-glow-hover hover:scale-105 group animate-fade-in-up"
                style={{animationDelay: '0.2s'}} asChild>
                <Link href={getLocalizedPath('/coffee-box-builder')}>
                  <Package className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform-smooth" />
                  {t('homepage.finalCta.cta')}
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* CTA Image - positioned absolutely to stick to bottom with same spacing as text */}
        <div className="hidden lg:block absolute bottom-0 right-0 animate-fade-in-up"
          style={{animationDelay: '0.3s'}}>
          <div className="pr-4 lg:pr-8"> {/* Same padding as container */}
            <div className="relative max-w-md w-full mr-auto"> {/* mr-auto to align with container edge */}
              <Image
                src="/images/cta-coffee-image.webp"
                alt="Coffee Box Builder - PrimeCaffe"
                width={400}
                height={300}
                className="w-full h-auto object-cover"
                sizes="(max-width: 1024px) 0px, 400px"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
